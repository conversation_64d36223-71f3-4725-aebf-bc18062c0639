"use client";

import { useState } from "react";
import { TimePicker } from "@/components/ui/time-picker";
import { MobileTimePicker } from "@/components/ui/mobile-time-picker";

export default function TestTimePickerPage() {
  const [time1, setTime1] = useState<string>("");
  const [time2, setTime2] = useState<string>("14:30"); // Pre-set time
  const [time3, setTime3] = useState<string>("");

  return (
    <div className="container mx-auto p-8 max-w-md">
      <h1 className="text-2xl font-bold mb-8">Time Picker Test</h1>
      
      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-2">Empty Time Picker (should default to next hour)</h2>
          <TimePicker
            value={time1}
            onChange={setTime1}
            use12Hour={true}
          />
          <p className="text-sm text-muted-foreground mt-1">
            Current value: {time1 || "empty"}
          </p>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">Pre-set Time Picker</h2>
          <TimePicker
            value={time2}
            onChange={setTime2}
            use12Hour={true}
          />
          <p className="text-sm text-muted-foreground mt-1">
            Current value: {time2}
          </p>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">Mobile Time Picker (Direct)</h2>
          <MobileTimePicker
            value={time3}
            onChange={setTime3}
            use12Hour={true}
          />
          <p className="text-sm text-muted-foreground mt-1">
            Current value: {time3 || "empty"}
          </p>
        </div>

        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Current Time Info</h3>
          <p className="text-sm">Current time: {new Date().toLocaleTimeString()}</p>
          <p className="text-sm">Next hour should be: {new Date(Date.now() + 60 * 60 * 1000).getHours()}:00</p>
        </div>
      </div>
    </div>
  );
}
