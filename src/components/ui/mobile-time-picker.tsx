"use client";

import * as React from "react";
import { Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { TimeWheelPicker } from "@/components/ui/time-wheel-picker";
import { useMediaQuery } from "@/hooks/use-media-query";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import "./mobile-time-picker.css";

export interface MobileTimePickerProps {
  value?: string; // HH:MM format (24-hour)
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  use12Hour?: boolean;
  // Optional external modal state control
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function MobileTimePicker({
  value,
  onChange,
  className,
  disabled = false,
  placeholder = "Select time",
  use12Hour = true,
  open: externalOpen,
  onOpenChange: externalOnOpenChange
}: MobileTimePickerProps) {
  const [internalOpen, setInternalOpen] = React.useState(false);

  // Use external state if provided, otherwise use internal state
  const isOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setIsOpen = externalOnOpenChange || setInternalOpen;

  // Simple initial state - will be updated when modal opens
  const [tempTime, setTempTime] = React.useState<{
    hour: string;
    minute: string;
    period: string;
  }>({ hour: "", minute: "", period: "AM" });
  
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Parse the current time value
  const parseTime = React.useCallback((timeStr?: string) => {
    if (!timeStr) return { hour: "", minute: "", period: "AM" };

    const [hoursStr, minutesStr] = timeStr.split(':');
    const hourNum = parseInt(hoursStr, 10);
    const minuteNum = parseInt(minutesStr, 10);

    // Validate input
    if (isNaN(hourNum) || isNaN(minuteNum) || hourNum < 0 || hourNum > 23 || minuteNum < 0 || minuteNum > 59) {
      return { hour: "", minute: "", period: "AM" };
    }

    if (use12Hour) {
      const period = hourNum >= 12 ? "PM" : "AM";
      let displayHour = hourNum;

      // Convert 24-hour to 12-hour format
      if (hourNum === 0) {
        displayHour = 12; // Midnight -> 12 AM
      } else if (hourNum > 12) {
        displayHour = hourNum - 12; // 13-23 -> 1-11 PM
      }

      return {
        hour: displayHour.toString(),
        minute: minuteNum.toString().padStart(2, '0'),
        period
      };
    }

    return {
      hour: hourNum.toString().padStart(2, '0'),
      minute: minuteNum.toString().padStart(2, '0'),
      period: "AM"
    };
  }, [use12Hour]);

  // Convert 12-hour format to 24-hour format
  const convertTo24Hour = React.useCallback((hour12: number, period: string): number => {
    if (hour12 === 12) {
      return period === "AM" ? 0 : 12;
    }
    return period === "PM" ? hour12 + 12 : hour12;
  }, []);

  // Format time for display
  const formatTimeDisplay = React.useCallback((timeStr?: string) => {
    if (!timeStr) return placeholder;
    
    const parsed = parseTime(timeStr);
    if (!parsed.hour || !parsed.minute) return placeholder;
    
    if (use12Hour) {
      return `${parsed.hour}:${parsed.minute} ${parsed.period}`;
    }
    return `${parsed.hour}:${parsed.minute}`;
  }, [parseTime, placeholder, use12Hour]);

  // Calculate default time values
  const getDefaultTime = React.useCallback(() => {
    const parsed = parseTime(value);

    // If we have a current value, use it
    if (parsed.hour && parsed.minute) {
      return parsed;
    }

    // If no current value, default to next closest hour
    const now = new Date();
    let nextHour = now.getHours() + 1; // Next hour
    if (nextHour >= 24) nextHour = 0; // Handle midnight rollover

    let defaultHour = "12";
    let defaultPeriod = "AM";

    if (use12Hour) {
      // Convert 24-hour to 12-hour format
      let hour12;
      if (nextHour === 0) {
        hour12 = 12; // Midnight becomes 12 AM
      } else if (nextHour === 12) {
        hour12 = 12; // Noon stays 12 PM
      } else if (nextHour < 12) {
        hour12 = nextHour; // 1-11 AM stays the same
      } else {
        hour12 = nextHour - 12; // 13-23 becomes 1-11 PM
      }
      defaultHour = hour12.toString();
      defaultPeriod = nextHour >= 12 ? "PM" : "AM";
    } else {
      defaultHour = nextHour.toString().padStart(2, '0');
    }

    return {
      hour: defaultHour,
      minute: "00", // Default to top of the hour
      period: defaultPeriod
    };
  }, [value, parseTime, use12Hour]);

  // Get effective time values (with defaults if tempTime is empty)
  const effectiveTime = React.useMemo(() => {
    if (tempTime.hour && tempTime.minute) {
      return tempTime;
    }

    // If tempTime is empty, use defaults
    const defaultTime = getDefaultTime();
    return {
      hour: tempTime.hour || defaultTime.hour,
      minute: tempTime.minute || defaultTime.minute,
      period: tempTime.period || defaultTime.period
    };
  }, [tempTime, getDefaultTime]);

  // Initialize temp time when opening modal
  const handleOpen = React.useCallback(() => {
    if (disabled) return;

    const defaultTime = getDefaultTime();
    setTempTime(defaultTime);
    setIsOpen(true);
  }, [disabled, getDefaultTime]);

  // Handle cancel - close without saving
  const handleCancel = React.useCallback(() => {
    setIsOpen(false);
  }, []);

  // Handle confirm - save the selected time
  const handleConfirm = React.useCallback(() => {
    const currentTime = effectiveTime;
    const hourNum = parseInt(currentTime.hour, 10);
    const minuteNum = parseInt(currentTime.minute, 10);

    if (isNaN(hourNum) || isNaN(minuteNum)) return;

    let hour24 = hourNum;
    if (use12Hour) {
      hour24 = convertTo24Hour(hourNum, currentTime.period);
    }

    const timeString = `${hour24.toString().padStart(2, '0')}:${currentTime.minute}`;
    onChange(timeString);
    setIsOpen(false);
  }, [effectiveTime, use12Hour, convertTo24Hour, onChange]);

  // Handle time wheel changes
  const handleTimeChange = React.useCallback((type: 'hour' | 'minute' | 'period', value: string) => {
    setTempTime(prev => ({
      ...prev,
      [type]: value
    }));
  }, []);

  // Generate hour options
  const hourOptions = React.useMemo(() => {
    if (use12Hour) {
      return Array.from({ length: 12 }, (_, i) => {
        const hour = i + 1;
        return {
          value: hour.toString(),
          label: hour.toString()
        };
      });
    } else {
      return Array.from({ length: 24 }, (_, i) => {
        const hour = i.toString().padStart(2, '0');
        return {
          value: hour,
          label: hour
        };
      });
    }
  }, [use12Hour]);

  // Generate minute options (5-minute increments)
  const minuteOptions = React.useMemo(() => {
    return Array.from({ length: 12 }, (_, i) => {
      const minute = (i * 5).toString().padStart(2, '0');
      return {
        value: minute,
        label: minute
      };
    });
  }, []);

  // Period options for 12-hour format
  const periodOptions = React.useMemo(() => [
    { value: "AM", label: "AM" },
    { value: "PM", label: "PM" }
  ], []);

  return (
    <>
      {/* Time Trigger Button */}
      <Button
        variant="outline"
        className={cn(
          "w-full justify-start text-left font-normal",
          !value && "text-muted-foreground",
          className
        )}
        disabled={disabled}
        onClick={handleOpen}
      >
        <Clock className="mr-2 h-4 w-4" />
        <span>{formatTimeDisplay(value)}</span>
      </Button>

      {/* Mobile Time Picker Modal */}
      <MobileDialog open={isOpen} onOpenChange={setIsOpen}>
        <MobileDialogContent
          className="p-0"
          fullHeight={false}
          enableSwipeToDismiss={!isDesktop}
        >
          <MobileDialogHeader className="sr-only">
            <VisuallyHidden>
              <MobileDialogTitle>Select Time</MobileDialogTitle>
            </VisuallyHidden>
          </MobileDialogHeader>

          <div className="flex flex-col">
            {/* Time Wheels Container */}
            <div
              className="flex items-center justify-center px-4 py-6"
              onTouchStart={(e) => {
                // Prevent modal dismiss when touching the wheels area
                e.stopPropagation();
              }}
              onTouchMove={(e) => {
                // Prevent modal dismiss when interacting with wheels
                e.stopPropagation();
              }}
              onTouchEnd={(e) => {
                // Prevent modal dismiss when finishing wheel interaction
                e.stopPropagation();
              }}
            >
              <div className="flex items-center gap-4 w-full max-w-sm">
                {/* Hour Wheel */}
                <div className="flex-1">
                  <TimeWheelPicker
                    options={hourOptions}
                    value={effectiveTime.hour}
                    onChange={(value) => handleTimeChange('hour', value)}
                    label="Hour"
                    hideLabel={true}
                  />
                </div>

                {/* Separator */}
                <div className="text-2xl font-bold text-muted-foreground">:</div>

                {/* Minute Wheel */}
                <div className="flex-1">
                  <TimeWheelPicker
                    options={minuteOptions}
                    value={effectiveTime.minute}
                    onChange={(value) => handleTimeChange('minute', value)}
                    label="Minute"
                    hideLabel={true}
                  />
                </div>

                {/* AM/PM Wheel for 12-hour format */}
                {use12Hour && (
                  <div className="flex-1">
                    <TimeWheelPicker
                      options={periodOptions}
                      value={effectiveTime.period}
                      onChange={(value) => handleTimeChange('period', value)}
                      label="Period"
                      hideLabel={true}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between p-4 bg-background">
              <Button
                variant="ghost"
                onClick={handleCancel}
                className="text-muted-foreground hover:text-foreground hover:bg-muted"
              >
                Cancel
              </Button>
              <Button
                variant="ghost"
                onClick={handleConfirm}
                className="text-muted-foreground hover:text-foreground hover:bg-muted"
              >
                OK
              </Button>
            </div>
          </div>
        </MobileDialogContent>
      </MobileDialog>
    </>
  );
}
