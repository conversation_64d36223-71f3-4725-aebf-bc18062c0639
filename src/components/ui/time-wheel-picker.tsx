"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import "./mobile-time-picker.css";

interface TimeWheelOption {
  value: string;
  label: string;
}

interface TimeWheelPickerProps {
  options: TimeWheelOption[];
  value: string;
  onChange: (value: string) => void;
  label: string;
  className?: string;
  hideLabel?: boolean;
}

export function TimeWheelPicker({
  options,
  value,
  onChange,
  label,
  className,
  hideLabel = false
}: TimeWheelPickerProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const [isScrolling, setIsScrolling] = React.useState(false);

  const itemHeight = 48; // Height of each option in pixels
  const visibleItems = 5; // Number of visible items
  const containerHeight = visibleItems * itemHeight;
  const centerIndex = Math.floor(visibleItems / 2);

  // Find the index of the current value
  React.useEffect(() => {
    const index = options.findIndex(option => option.value === value);
    if (index >= 0 && index !== selectedIndex) {
      setSelectedIndex(index);
    }
  }, [options, value, selectedIndex]);

  // Scroll to selected item
  const scrollToIndex = React.useCallback((index: number) => {
    if (!containerRef.current) return;

    const targetScrollTop = index * itemHeight;
    containerRef.current.scrollTo({
      top: targetScrollTop,
      behavior: isScrolling ? 'auto' : 'smooth'
    });
  }, [itemHeight, isScrolling]);

  // Initialize scroll position
  React.useEffect(() => {
    scrollToIndex(selectedIndex);
  }, [selectedIndex, scrollToIndex]);

  // Handle scroll events to update selected index
  const handleScroll = React.useCallback(() => {
    if (!containerRef.current || isScrolling) return;

    const scrollTop = containerRef.current.scrollTop;
    const newIndex = Math.round(scrollTop / itemHeight);
    const clampedIndex = Math.max(0, Math.min(newIndex, options.length - 1));

    if (clampedIndex !== selectedIndex) {
      setSelectedIndex(clampedIndex);
      onChange(options[clampedIndex].value);
    }
  }, [itemHeight, options, selectedIndex, onChange, isScrolling]);

  // Handle scroll end to snap to nearest item
  const scrollTimeoutRef = React.useRef<NodeJS.Timeout>();
  const handleScrollEnd = React.useCallback(() => {
    if (!containerRef.current) return;

    const scrollTop = containerRef.current.scrollTop;
    const nearestIndex = Math.round(scrollTop / itemHeight);
    const clampedIndex = Math.max(0, Math.min(nearestIndex, options.length - 1));

    // Calculate the exact scroll position for the nearest item
    const targetScrollTop = clampedIndex * itemHeight;

    // Only snap if we're not already at the exact position
    if (Math.abs(scrollTop - targetScrollTop) > 1) {
      containerRef.current.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }

    // Update selected index and trigger onChange
    if (clampedIndex !== selectedIndex) {
      setSelectedIndex(clampedIndex);
      onChange(options[clampedIndex].value);
    }

    setIsScrolling(false);
  }, [itemHeight, options, selectedIndex, onChange]);

  // Handle scroll start
  const handleScrollStart = React.useCallback(() => {
    setIsScrolling(true);
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
  }, []);

  // Handle scroll events with snapping
  const handleScrollEvent = React.useCallback(() => {
    if (!containerRef.current) return;

    handleScrollStart();

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set new timeout for scroll end detection
    scrollTimeoutRef.current = setTimeout(() => {
      handleScrollEnd();
    }, 150);
  }, [handleScrollStart, handleScrollEnd]);

  // Calculate visual feedback for items
  const getItemOpacity = React.useCallback((index: number) => {
    const distance = Math.abs(index - selectedIndex);
    if (distance === 0) return 1;
    if (distance === 1) return 0.7;
    if (distance === 2) return 0.4;
    return 0.2;
  }, [selectedIndex]);

  const getItemScale = React.useCallback((index: number) => {
    const distance = Math.abs(index - selectedIndex);
    if (distance === 0) return 1;
    if (distance === 1) return 0.95;
    return 0.9;
  }, [selectedIndex]);

  return (
    <div className={cn("relative", className)}>
      {/* Label */}
      {!hideLabel && (
        <div className="text-xs text-muted-foreground text-center mb-2 font-medium">
          {label}
        </div>
      )}
      
      {/* Wheel Container */}
      <div 
        className="relative overflow-hidden"
        style={{ height: containerHeight }}
      >
        {/* Selection Indicator */}
        <div 
          className="absolute inset-x-0 bg-muted/30 border-y border-border pointer-events-none z-10"
          style={{ 
            top: Math.floor(visibleItems / 2) * itemHeight,
            height: itemHeight 
          }}
        />
        
        {/* Scrollable Options */}
        <div
          ref={containerRef}
          className="h-full overflow-y-scroll scrollbar-hide time-wheel-container"
          onScroll={handleScrollEvent}
          onTouchStart={(e) => {
            // Prevent modal dismiss when touching the wheel
            e.stopPropagation();
          }}
          onTouchMove={(e) => {
            // Prevent modal dismiss when scrolling the wheel
            e.stopPropagation();
          }}
          onTouchEnd={(e) => {
            // Prevent modal dismiss when finishing wheel scroll
            e.stopPropagation();
          }}
        >
          {/* Render all items with proper spacing */}
          {Array.from({ length: options.length + visibleItems - 1 }, (_, i) => {
            const optionIndex = i - centerIndex;
            const isValidOption = optionIndex >= 0 && optionIndex < options.length;

            if (!isValidOption) {
              // Empty spacer item
              return (
                <div
                  key={`spacer-${i}`}
                  className="flex items-center justify-center"
                  style={{ height: itemHeight }}
                />
              );
            }

            const option = options[optionIndex];
            return (
              <motion.div
                key={option.value}
                className="flex items-center justify-center cursor-pointer select-none"
                style={{
                  height: itemHeight,
                  opacity: getItemOpacity(optionIndex),
                  scale: getItemScale(optionIndex),
                }}
                animate={{
                  opacity: getItemOpacity(optionIndex),
                  scale: getItemScale(optionIndex),
                }}
                transition={{ duration: 0.2 }}
                onClick={() => {
                  setSelectedIndex(optionIndex);
                  scrollToIndex(optionIndex);
                  onChange(option.value);
                }}
              >
                <span className="text-lg font-medium">
                  {option.label}
                </span>
              </motion.div>
            );
          })}
        </div>
        
        {/* Gradient Overlays */}
        <div className="absolute top-0 left-0 right-0 h-12 bg-gradient-to-b from-background to-transparent pointer-events-none" />
        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-background to-transparent pointer-events-none" />
      </div>
    </div>
  );
}
